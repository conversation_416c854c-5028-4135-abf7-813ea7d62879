'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON> } from '@/types/game';

export default function GamePage() {
  const [lobby, setLobby] = useState<Lobby | null>(null);
  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null);
  const [wordInput, setWordInput] = useState('');
  const [timeLeft, setTimeLeft] = useState(0);
  const [error, setError] = useState('');
  const router = useRouter();
  const params = useParams();
  const lobbyId = params?.id ? (params.id as string) : '';

  useEffect(() => {
    const username = localStorage.getItem('wordspy-username');
    if (!username) {
      router.push('/');
      return;
    }

    // Poll for game updates every 1 second
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/lobbies?lobbyId=${lobbyId}`);
        if (response.ok) {
          const lobbies = await response.json();
          const currentLobby = lobbies.find((l: Lobby) => l.id === lobbyId);
          if (currentLobby) {
            setLobby(currentLobby);
            const player = currentLobby.players.find((p: Player) => p.username === username);
            setCurrentPlayer(player || null);
            setTimeLeft(currentLobby.gameState.timeLeft);

            // Check if game ended or returned to lobby
            if (currentLobby.gameState.phase === 'waiting') {
              router.push(`/lobby/${lobbyId}`);
            }
          }
        }
      } catch (error) {
        console.error('Error polling game:', error);
      }
    }, 1000);

    return () => {
      clearInterval(pollInterval);
    };
  }, [lobbyId, router]);

  // Timer countdown
  useEffect(() => {
    if (timeLeft > 0 && lobby?.gameState.phase === 'playing') {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft, lobby?.gameState.phase]);

  const handleSubmitWord = async (e: React.FormEvent) => {
    e.preventDefault();
    if (wordInput.trim() && currentPlayer) {
      try {
        const response = await fetch('/api/lobbies', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'submit-word',
            lobbyId,
            playerId: currentPlayer.id,
            word: wordInput.trim()
          })
        });

        if (response.ok) {
          setWordInput('');
          const updatedLobby = await response.json();
          setLobby(updatedLobby);
          const player = updatedLobby.players.find((p: Player) => p.username === currentPlayer.username);
          setCurrentPlayer(player || null);
        } else {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to submit word');
          setTimeout(() => setError(''), 5000);
        }
      } catch (error) {
        console.error('Error submitting word:', error);
        setError('Failed to submit word');
        setTimeout(() => setError(''), 5000);
      }
    }
  };

  const handleVote = async (playerId: string) => {
    if (currentPlayer) {
      try {
        const response = await fetch('/api/lobbies', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'submit-vote',
            lobbyId,
            playerId: currentPlayer.id,
            votedPlayerId: playerId
          })
        });

        if (response.ok) {
          const updatedLobby = await response.json();
          setLobby(updatedLobby);
          const player = updatedLobby.players.find((p: Player) => p.username === currentPlayer.username);
          setCurrentPlayer(player || null);
        } else {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to submit vote');
          setTimeout(() => setError(''), 5000);
        }
      } catch (error) {
        console.error('Error submitting vote:', error);
        setError('Failed to submit vote');
        setTimeout(() => setError(''), 5000);
      }
    }
  };

  const handleBackToLobby = () => {
    router.push('/');
  };

  if (!lobby || !currentPlayer) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Indlæser spil...</p>
        </div>
      </div>
    );
  }

  const activePlayers = lobby.players.filter(p => p.isConnected && !lobby.gameState.eliminatedPlayers.includes(p.id));
  const currentTurnPlayer = activePlayers[lobby.gameState.currentPlayerIndex];
  const isMyTurn = currentTurnPlayer?.id === currentPlayer.id;
  const hasSubmitted = currentPlayer.hasSubmittedWord;
  const isEliminated = lobby.gameState.eliminatedPlayers.includes(currentPlayer.id);

  if (lobby.gameState.phase === 'ended') {
    const imposter = lobby.players.find(p => p.actualWord === lobby.gameState.words.imposter);
    const imposterWon = !lobby.gameState.eliminatedPlayers.includes(imposter?.id || '');
    
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <h1 className="text-3xl font-bold mb-6">Spil Slut!</h1>
              
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">
                  {imposterWon ? 'Imposteren Vinder!' : 'Borgerne Vinder!'}
                </h2>
                
                <div className="space-y-4">
                  <div className="p-4 bg-red-100 rounded-lg">
                    <h3 className="font-semibold text-red-800">Imposteren</h3>
                    <p className="text-red-700">
                      {imposter?.username} havde ordet: <strong>{imposter?.actualWord}</strong>
                    </p>
                  </div>
                  
                  <div className="p-4 bg-blue-100 rounded-lg">
                    <h3 className="font-semibold text-blue-800">Alle Andre</h3>
                    <p className="text-blue-700">
                      Havde ordet: <strong>{lobby.gameState.words.normal}</strong>
                    </p>
                  </div>
                </div>
              </div>

              <button
                onClick={handleBackToLobby}
                className="px-6 py-3 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-colors"
              >
                Tilbage til Lobbies
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">WordSpy</h1>
          <p className="text-gray-600">Runde {lobby.gameState.round}</p>
        </header>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 max-w-2xl mx-auto">
            {error}
          </div>
        )}

        <div className="max-w-4xl mx-auto">
          {/* Your Word */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-bold mb-4 text-center">Dit Ord</h2>
            <div className="text-center">
              <span className="text-3xl font-bold text-blue-600 bg-blue-100 px-6 py-3 rounded-lg">
                {currentPlayer.word}
              </span>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Game Area */}
            <div className="bg-white rounded-lg shadow-md p-6">
              {lobby.gameState.phase === 'playing' && (
                <>
                  <h2 className="text-xl font-bold mb-4">
                    Tur {lobby.gameState.currentTurn} af {activePlayers.length}
                  </h2>
                  
                  {timeLeft > 0 && (
                    <div className="mb-4 text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {timeLeft}s
                      </div>
                    </div>
                  )}

                  {isMyTurn && !hasSubmitted && !isEliminated && (
                    <form onSubmit={handleSubmitWord} className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Beskriv dit ord med ÉT ord:
                      </label>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={wordInput}
                          onChange={(e) => setWordInput(e.target.value)}
                          placeholder="Indtast ét ord..."
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          maxLength={20}
                          required
                        />
                        <button
                          type="submit"
                          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        >
                          Indsend
                        </button>
                      </div>
                    </form>
                  )}

                  {!isMyTurn && (
                    <p className="text-gray-600 mb-4">
                      {currentTurnPlayer?.hasSubmittedWord ? (
                        <>Venter på næste spiller...</>
                      ) : (
                        <>Venter på <strong>{currentTurnPlayer?.username}</strong> indsender deres ord...</>
                      )}
                    </p>
                  )}

                  {hasSubmitted && (
                    <p className="text-green-600 mb-4">✓ Du har indsendt dit ord</p>
                  )}

                  {isEliminated && (
                    <p className="text-red-600 mb-4">Du er blevet elimineret</p>
                  )}

                  {/* Submissions */}
                  <div className="mt-6">
                    <h3 className="font-semibold mb-2">Indsendte Ord:</h3>
                    <div className="space-y-2">
                      {Object.entries(lobby.gameState.submissions).map(([playerId, word]) => {
                        const player = lobby.players.find(p => p.id === playerId);
                        return (
                          <div key={playerId} className="flex items-center gap-2">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: player?.color }}
                            ></div>
                            <span className="font-medium">{player?.username}:</span>
                            <span>{word}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </>
              )}

              {lobby.gameState.phase === 'voting' && !isEliminated && (
                <>
                  <h2 className="text-xl font-bold mb-4">Afstemning!</h2>
                  <p className="text-gray-600 mb-4">
                    Stem på hvem du tror er imposter:
                  </p>
                  
                  {!currentPlayer.votedFor ? (
                    <div className="space-y-2">
                      {activePlayers.filter(p => p.id !== currentPlayer.id).map((player) => (
                        <button
                          key={player.id}
                          onClick={() => handleVote(player.id)}
                          className="w-full flex items-center gap-3 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div
                            className="w-6 h-6 rounded-full"
                            style={{ backgroundColor: player.color }}
                          ></div>
                          <span className="font-medium">{player.username}</span>
                        </button>
                      ))}
                    </div>
                  ) : (
                    <p className="text-green-600">✓ Du har stemt</p>
                  )}
                </>
              )}
            </div>

            {/* Players List */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold mb-4">Spillere</h2>
              <div className="space-y-2">
                {lobby.players.filter(p => p.isConnected).map((player) => {
                  const isEliminated = lobby.gameState.eliminatedPlayers.includes(player.id);
                  return (
                    <div
                      key={player.id}
                      className={`flex items-center gap-3 p-2 rounded-lg ${
                        isEliminated ? 'opacity-50 bg-red-50' : ''
                      }`}
                    >
                      <div
                        className="w-6 h-6 rounded-full border-2 border-gray-300"
                        style={{ backgroundColor: player.color }}
                      ></div>
                      <span className={`font-medium ${isEliminated ? 'line-through' : ''}`}>
                        {player.username}
                      </span>
                      {player.hasSubmittedWord && lobby.gameState.phase === 'playing' && (
                        <span className="text-green-500 text-sm">✓</span>
                      )}
                      {player.votedFor && lobby.gameState.phase === 'voting' && (
                        <span className="text-blue-500 text-sm">Stemt</span>
                      )}
                      {isEliminated && (
                        <span className="text-red-500 text-sm">Elimineret</span>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
