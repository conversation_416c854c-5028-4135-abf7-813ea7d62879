'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { <PERSON><PERSON>, Player, PLAYER_COLORS } from '@/types/game';
import { gameAPI } from '@/lib/api';

export default function LobbyPage() {
  const [lobby, setLobby] = useState<Lobby | null>(null);
  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null);
  const [error, setError] = useState('');
  const router = useRouter();
  const params = useParams();
  const lobbyId = params?.id ? (params.id as string) : '';

  useEffect(() => {
    const username = localStorage.getItem('wordspy-username');
    if (!username) {
      router.push('/');
      return;
    }

    // Use HTTP polling instead of Socket.IO for now
    const joinLobby = async () => {
      try {
        const player: Player = {
          id: gameAPI.getPlayerId(),
          username,
          color: PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)],
          isHost: false,
          isConnected: true
        };

        const updatedLobby = await gameAPI.joinLobby(lobbyId, player);
        setLobby(updatedLobby);
        const currentPlayer = updatedLobby.players.find((p: Player) => p.username === username);
        setCurrentPlayer(currentPlayer || null);
      } catch (error) {
        console.error('Error joining lobby:', error);
        setError(error instanceof Error ? error.message : 'Failed to connect to lobby');
        setTimeout(() => setError(''), 5000);
      }
    };

    joinLobby();

    // Poll for lobby updates every 2 seconds
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/lobbies?lobbyId=${lobbyId}`);
        if (response.ok) {
          const lobbies = await response.json();
          const currentLobby = lobbies.find((l: Lobby) => l.id === lobbyId);
          if (currentLobby) {
            setLobby(currentLobby);
            const player = currentLobby.players.find((p: Player) => p.username === username);
            setCurrentPlayer(player || null);

            // Check if game started
            if (currentLobby.gameState.phase === 'playing') {
              router.push(`/game/${lobbyId}`);
            }
          }
        }
      } catch (error) {
        console.error('Error polling lobby:', error);
      }
    }, 2000);

    return () => {
      clearInterval(pollInterval);
    };
  }, [lobbyId, router]);

  const handleStartGame = async () => {
    if (currentPlayer?.isHost) {
      try {
        const updatedLobby = await gameAPI.startGame(lobbyId);
        setLobby(updatedLobby);
        if (updatedLobby.gameState.phase === 'playing') {
          router.push(`/game/${lobbyId}`);
        }
      } catch (error) {
        console.error('Error starting game:', error);
        setError(error instanceof Error ? error.message : 'Failed to start game');
        setTimeout(() => setError(''), 5000);
      }
    }
  };

  const handleColorChange = async (color: string) => {
    if (currentPlayer) {
      try {
        const updatedLobby = await gameAPI.updateColor(lobbyId, color);
        setLobby(updatedLobby);
        const player = updatedLobby.players.find((p: Player) => p.username === currentPlayer.username);
        setCurrentPlayer(player || null);
      } catch (error) {
        console.error('Error updating color:', error);
        setError(error instanceof Error ? error.message : 'Failed to update color');
        setTimeout(() => setError(''), 5000);
      }
    }
  };

  const handleLeaveLobby = () => {
    router.push('/');
  };

  if (!lobby) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Indlæser lobby...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">{lobby.name}</h1>
          <p className="text-gray-600">Venter på spillere...</p>
          <button
            onClick={handleLeaveLobby}
            className="mt-2 text-sm text-blue-500 hover:text-blue-700"
          >
            ← Tilbage til Lobbies
          </button>
        </header>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 max-w-2xl mx-auto">
            {error}
          </div>
        )}

        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">
              Spillere ({lobby.players.filter(p => p.isConnected).length}/{lobby.maxPlayers})
            </h2>
            <div className="space-y-3">
              {lobby.players.filter(p => p.isConnected).map((player) => (
                <div
                  key={player.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-6 h-6 rounded-full border-2 border-gray-300"
                      style={{ backgroundColor: player.color }}
                    ></div>
                    <span className="font-medium">{player.username}</span>
                    {player.isHost && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        Vært
                      </span>
                    )}
                  </div>
                  {player.username === currentPlayer?.username && (
                    <div className="flex gap-1">
                      {PLAYER_COLORS.map((color) => (
                        <button
                          key={color}
                          onClick={() => handleColorChange(color)}
                          className={`w-6 h-6 rounded-full border-2 ${
                            player.color === color ? 'border-gray-800' : 'border-gray-300'
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {currentPlayer?.isHost && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-bold mb-4">Spil Indstillinger</h3>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  • Mindst 3 spillere er nødvendige for at starte
                  • En tilfældig spiller vil være imposter
                  • Spillere tager tur til at beskrive deres ord
                  • Stem for at eliminere imposteren
                </div>
                <button
                  onClick={handleStartGame}
                  disabled={lobby.players.filter(p => p.isConnected).length < 3}
                  className="w-full px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {lobby.players.filter(p => p.isConnected).length < 3
                    ? `Skal bruges ${3 - lobby.players.filter(p => p.isConnected).length} flere spillere`
                    : 'Start Spil'
                  }
                </button>
              </div>
            </div>
          )}

          {!currentPlayer?.isHost && (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-600">Venter på at vært starter spillet...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
